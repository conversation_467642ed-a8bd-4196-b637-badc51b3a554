[bumpversion]
current_version = 1.4.0
commit = True
tag = True

[bumpversion:file:setup.py]
search = version='{current_version}'
replace = version='{new_version}'

[bumpversion:file:face_recognition/__init__.py]
search = __version__ = '{current_version}'
replace = __version__ = '{new_version}'

[bdist_wheel]
universal = 1

[flake8]
exclude =
    .github,
    .idea,
    .eggs,
    examples,
    docs,
    .tox,
    bin,
    dist,
    tools,
    *.egg-info,
    __init__.py,
    *.yml
max-line-length = 160
