FROM animcogn/face_recognition:cpu

RUN useradd -ms /bin/bash jovyan && \
    chown -R jovyan:jovyan /opt/venv && \
    echo 'PATH="/opt/venv/bin:$PATH"' >> /home/<USER>/.bashrc

USER jovyan

ENV PATH="/opt/venv/bin:$PATH"

RUN pip3 install jupyterlab

ENV NB_PREFIX /

CMD ["sh", "-c", "jupyter lab --notebook-dir=/home/<USER>'' --NotebookApp.password='' --NotebookApp.allow_origin='*' --NotebookApp.base_url=${NB_PREFIX}"]
