version: '2.3'

services:
  face_recognition:
    image: face_recognition
    container_name: face_recognition
    working_dir: /face_recognition/examples
    build:
      context: .
      #Uncomment this line to run the example on the GPU (requires Nvidia-Docker)
      # dockerfile: Dockerfile.gpu 
    command: python3 -u find_faces_in_picture_cnn.py
    volumes:
      - ./:/face_recognition
    #Uncomment this line to run the example on the GPU (requires Nvidia-Docker)
    # runtime: nvidia